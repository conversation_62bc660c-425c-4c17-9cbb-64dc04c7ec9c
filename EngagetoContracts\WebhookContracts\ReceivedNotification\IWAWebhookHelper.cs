﻿using EngagetoContracts.Services;
using EngagetoEntities.Entities;

namespace EngagetoContracts.WebhookContracts.ReceivedNotification
{
    public interface IWAWebhookHelper : ITransientServiceWithScoped
    {
        Task InboxSettingMessageAsync(string businessId, string phoneNumber, string countryCode);
        Task UpdateContactChatStatusAsync(Contacts contact);
        Task ProcessTemplateAsync(EngagetoEntities.Entities.Template template, List<string> values, string companyId, string phoneNumber);
    }
}
