﻿using DocumentFormat.OpenXml.Math;
using EngagetoEntities.Dtos.BusinessDetailsDtos;
using EngagetoEntities.Entities;
using Microsoft.AspNetCore.Http;

namespace EngagetoContracts.UserContracts
{
    public interface IClientDetailsService
    {
        // Task<AddClientResult> AddClientAsync(Guid currentUserId, ClientDetailsDto clientDetailsDto, IFormFile? companylogo=null);
        Task<bool> AddClientAsync(Guid currentUserId, Ahex_CRMClientDetailsDto clientDetails);
        Task<bool> AddClientAnonymousAsync(Ahex_CRMClientDetailsDto clientDetails);
        Task<bool> UpdateClientAsync(Guid currentUserId, Guid clientId, Ahex_CRMClientDetailsUpdateDto updateClientDetails);
        Task<bool> DeleteClientAsync(Guid currentUserId, Guid clientId);
        Task<object> GetClientByIdAsync(Guid currentUserId, Guid clientId);
        Task<IEnumerable<object>> GetAllClientsAsync(Guid currentUserId, string searchQuery, bool includeInactive, string sortBy, bool isSortAscending);
        Task<string?> UploadLogoAsync(Guid clientId, IFormFile file);
        Task<BusinessMetaDetailsDto> GetClientDetailsAsync(string businessId);
        Task UpdateMetaAccountByWebhookAsync(string waAccountId, long messageLimit, string businessStatus, string tier,string? displayNumber);
        Task<List<TenantDtos>> GetTenantAccountsAsync(string tenant);
        Task<bool> UpdateRateLimit(string businessId, int Adhoclimit );
    }
}
