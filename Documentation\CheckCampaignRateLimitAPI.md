# Check Campaign Rate Limit API

## Overview

The `CheckCampaignRateLimit` API allows you to validate a list of contact phone numbers against the Meta WhatsApp Business API rolling 24-hour rate limits before creating a campaign. This helps determine if a campaign can proceed immediately or needs to be split/scheduled.

## API Endpoint

**Method**: `POST`  
**URL**: `/api/campaigns/check-rate-limit`  
**Authorization**: Required (JWT <PERSON>er <PERSON>)

## Request

### Parameters

- **Body** (JSON): `CheckRateLimitRequestDto`
- **Query** (Optional): `businessId` (GUID) - If not provided, uses user's business context

### Request DTO Structure

```json
{
  "phoneNumbers": [
    "+************",
    "9876543211",
    "(*************",
    "************"
  ],
  "businessId": "12345678-1234-1234-1234-123456789012" // Optional
}
```

### Request Properties

| Property | Type | Required | Description |
|----------|------|----------|-------------|
| `phoneNumbers` | `List<string>` | Yes | Array of phone numbers to check |
| `businessId` | `string` | No | Business ID (uses user context if not provided) |

## Response

### Success Response Structure

```json
{
  "success": true,
  "message": "Rate limit check completed successfully. Campaign can proceed.",
  "data": {
    "uniqueCount": 123,
    "availableCount": 200,
    "totalContactsProvided": 150,
    "alreadyMessagedCount": 27,
    "canProceed": true,
    "rateLimitInfo": {
      "metaTotalLimit": 1000,
      "totalLimit": 950,
      "adhocLimit": 50,
      "availableCount": 200,
      "consumeCount": 750
    },
    "message": "Campaign can proceed. 123 new contacts will be messaged, 77 slots will remain available."
  }
}
```

### Response Properties

| Property | Type | Description |
|----------|------|-------------|
| `uniqueCount` | `int` | Contacts that have NOT been messaged in last 24 hours (will count against rate limit) |
| `availableCount` | `int` | Available message slots in the 24-hour window |
| `totalContactsProvided` | `int` | Total number of contacts in the request |
| `alreadyMessagedCount` | `int` | Contacts already messaged in last 24 hours (won't count against rate limit) |
| `canProceed` | `bool` | Whether campaign can proceed (`uniqueCount <= availableCount`) |
| `rateLimitInfo` | `RateLimitCountDto` | Detailed rate limit information |
| `message` | `string` | User-friendly guidance message |

### Rate Limit Info Properties

| Property | Type | Description |
|----------|------|-------------|
| `metaTotalLimit` | `long` | Meta's total rate limit for the business tier |
| `totalLimit` | `long` | Effective limit after adhoc deductions |
| `adhocLimit` | `long` | Adhoc limit deduction |
| `availableCount` | `int` | Available slots for new messages |
| `consumeCount` | `int` | Messages already sent in last 24 hours |

## Usage Examples

### Example 1: Campaign Can Proceed

**Request:**
```bash
curl -X POST "https://your-api-domain/api/campaigns/check-rate-limit" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "phoneNumbers": [
      "+************",
      "+************",
      "+************"
    ]
  }'
```

**Response:**
```json
{
  "success": true,
  "message": "Rate limit check completed successfully. Campaign can proceed.",
  "data": {
    "uniqueCount": 3,
    "availableCount": 200,
    "totalContactsProvided": 3,
    "alreadyMessagedCount": 0,
    "canProceed": true,
    "message": "Campaign can proceed. 3 new contacts will be messaged, 197 slots will remain available."
  }
}
```

### Example 2: Rate Limit Exceeded

**Request:**
```bash
curl -X POST "https://your-api-domain/api/campaigns/check-rate-limit" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "phoneNumbers": ["array of 300 phone numbers..."]
  }'
```

**Response:**
```json
{
  "success": true,
  "message": "Rate limit check completed. Campaign exceeds available rate limit.",
  "data": {
    "uniqueCount": 300,
    "availableCount": 50,
    "totalContactsProvided": 300,
    "alreadyMessagedCount": 0,
    "canProceed": false,
    "message": "Rate limit exceeded. 300 contacts need messaging but only 50 slots available. Consider splitting campaign or scheduling 250 contacts for later."
  }
}
```

### Example 3: Some Contacts Already Messaged

**Response:**
```json
{
  "success": true,
  "message": "Rate limit check completed successfully. Campaign can proceed.",
  "data": {
    "uniqueCount": 75,
    "availableCount": 100,
    "totalContactsProvided": 150,
    "alreadyMessagedCount": 75,
    "canProceed": true,
    "message": "Campaign can proceed. 75 new contacts will be messaged, 25 slots will remain available."
  }
}
```

## Error Responses

### Missing Phone Numbers
```json
{
  "success": false,
  "message": "Phone numbers list is required and cannot be empty."
}
```

### Missing Business ID
```json
{
  "success": false,
  "message": "Business ID is required."
}
```

### Server Error
```json
{
  "success": false,
  "message": "Error checking campaign rate limit: [error details]",
  "errors": "[stack trace]"
}
```

## Business Logic

### Phone Number Processing
1. **Cleaning**: Removes spaces, dashes, parentheses, dots
2. **Normalization**: Adds country code (+91) if missing
3. **Validation**: Ensures minimum 10 digits
4. **Deduplication**: Uses case-insensitive comparison

### Rate Limit Calculation
1. **Get Rate Limits**: Fetches business tier limits and current consumption
2. **Check History**: Queries conversations from last 24 hours
3. **Filter Status**: Only counts sent/delivered/read messages
4. **Count Unique**: Determines new contacts vs already messaged
5. **Validate**: Compares unique count against available slots

### Rolling 24-Hour Window
- **Window**: Exactly 24 hours from current time (not calendar day)
- **Basis**: Unique customers messaged (not total messages)
- **Status**: Only successful messages (sent/delivered/read) count
- **Reset**: Continuously rolling, not fixed daily reset

## Integration Scenarios

### Frontend Campaign Creation Flow
1. **User selects contacts** → Call `check-rate-limit` API
2. **If `canProceed: true`** → Proceed with campaign creation
3. **If `canProceed: false`** → Show options:
   - Split campaign into smaller batches
   - Schedule excess contacts for later
   - Remove some contacts

### Batch Processing
```javascript
// Example frontend logic
const response = await checkRateLimit(phoneNumbers);
if (response.data.canProceed) {
  // Create campaign immediately
  await createCampaign(phoneNumbers);
} else {
  // Split into batches
  const batchSize = response.data.availableCount;
  const batch1 = phoneNumbers.slice(0, batchSize);
  const batch2 = phoneNumbers.slice(batchSize);
  
  await createCampaign(batch1); // Send now
  await scheduleCampaign(batch2, futureTime); // Schedule later
}
```

## Performance Considerations

- **Caching**: Rate limit info is calculated in real-time
- **Database Queries**: Uses generic repository with optimized filters
- **Phone Cleaning**: Processed in-memory for performance
- **Batch Size**: Recommended to check up to 1000 contacts per request

## Related APIs

- `GET /api/campaigns/rate-limit-counts` - Get current rate limit status
- `POST /api/campaigns/CampaignUploadExcelFile` - Upload with built-in rate limit validation
- Campaign creation APIs - Use this for pre-validation
