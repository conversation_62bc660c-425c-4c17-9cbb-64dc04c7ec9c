Stack trace:
Frame         Function      Args
0007FFFFA730  00021005FEBA (000210285F48, 00021026AB6E, 000000000000, 0007FFFF9630) msys-2.0.dll+0x1FEBA
0007FFFFA730  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFAA08) msys-2.0.dll+0x67F9
0007FFFFA730  000210046832 (000210285FF9, 0007FFFFA5E8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFA730  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFFA730  0002100690B4 (0007FFFFA740, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFFAA10  00021006A49D (0007FFFFA740, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFD0DF40000 ntdll.dll
7FFD0C4D0000 KERNEL32.DLL
7FFD0B860000 KERNELBASE.dll
7FFD0DD20000 USER32.dll
7FFD0B2B0000 win32u.dll
7FFD0BD50000 GDI32.dll
7FFD0B510000 gdi32full.dll
7FFD0B460000 msvcp_win.dll
7FFD0B650000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFD0CB60000 advapi32.dll
7FFD0D8E0000 msvcrt.dll
7FFD0D6C0000 sechost.dll
7FFD0BD80000 RPCRT4.dll
7FFD0A690000 CRYPTBASE.DLL
7FFD0B210000 bcryptPrimitives.dll
7FFD0CA70000 IMM32.DLL
