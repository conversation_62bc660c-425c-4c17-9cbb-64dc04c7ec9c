using System.ComponentModel.DataAnnotations;

namespace EngagetoEntities.Dtos.CampaignDtos
{
    /// <summary>
    /// Request DTO for checking campaign rate limits against a list of phone numbers
    /// </summary>
    public class CheckRateLimitRequestDto
    {
        [Required]
        public List<string> PhoneNumbers { get; set; } = new();
        public string? BusinessId { get; set; }
    }

    /// <summary>
    /// Response DTO for campaign rate limit check
    /// </summary>
    public class CheckRateLimitResponseDto
    {
        /// <summary>
        /// Count of unique contacts that have NOT been messaged in the last 24 hours
        /// These are the contacts that will count against the rate limit
        /// </summary>
        public int UniqueCount { get; set; }

        /// <summary>
        /// Available message count remaining in the 24-hour window
        /// Calculated as: TotalLimit - ConsumedCount
        /// </summary>
        public int AvailableCount { get; set; }

        /// <summary>
        /// Total contacts provided in the request
        /// </summary>
        public int TotalContactsProvided { get; set; }

        /// <summary>
        /// Count of contacts that were already messaged in the last 24 hours
        /// These contacts won't count against the rate limit
        /// </summary>
        public int AlreadyMessagedCount { get; set; }

        /// <summary>
        /// Whether the campaign can proceed within rate limits
        /// True if UniqueCount <= AvailableCount
        /// </summary>
        public bool CanProceed { get; set; }

        /// <summary>
        /// Current rate limit information
        /// </summary>
        public RateLimitCountDto RateLimitInfo { get; set; } = new();

        /// <summary>
        /// Additional message for frontend guidance
        /// </summary>
        public string? Message { get; set; }
    }
}
