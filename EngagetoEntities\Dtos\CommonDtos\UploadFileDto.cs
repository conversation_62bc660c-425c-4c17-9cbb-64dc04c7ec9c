﻿using Microsoft.AspNetCore.Http;
using EngagetoEntities.Dtos.CampaignDtos;

namespace EngagetoEntities.Dtos.CommonDtos
{
    public class UploadFileDto
    {
        public IFormFile File { get; set; }
        public string? ModuleType { get; set; }
    }
    public class ViewUploadFileDto
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string Url { get; set; }
        public string UploadedFileName { get; set; }
    }
    public class ViewUploadedFileDto
    {
        public int Id { get; set; }
        public string FileName { get; set; }
        public string FilePath { get; set; }
        public string UploadedFileName { get; set; }

    }

    public class CampaignUploadResultDto
    {
        public ViewUploadFileDto? UploadResult { get; set; }
        public int ContactCount { get; set; }
        public bool HasRateLimitExceeded { get; set; }
        public bool HasInsufficientBalance { get; set; }

        // Enhanced rate limit information (similar to CheckRateLimitResponseDto)
        public int UniqueCount { get; set; }
        public int AvailableCount { get; set; }
        public int AlreadyConsumedCount { get; set; }
        public bool CanProceed { get; set; }
        public RateLimitCountDto? RateLimitInfo { get; set; }
        public string? RateLimitMessage { get; set; }

    }


}
