# Excel File Rate Limit Validation

## Overview

This feature allows you to validate an uploaded Excel file containing WhatsApp campaign data against the current WhatsApp Business API rate limits. The system extracts unique phone numbers from the Excel file and compares the count with the available message quota for the last 24 hours.

## Implementation Details

### Method: `ValidateExcelFileAgainstRateLimitAsync`

**Location**: `EngagetoRepository/Services/CampaignService.cs`

**Purpose**: Processes an uploaded Excel file and checks if the unique phone numbers can be sent within the current rate limit.

**Parameters**:
- `fileDto` (UploadFileDto): The uploaded Excel file containing campaign data
- `businessId` (string): The business ID to check rate limits for

**Returns**: `Task<bool>` - True if messages can be sent within rate limit; otherwise, false

### API Endpoint

**Endpoint**: `POST /api/campaigns/validate-excel-rate-limit`

**Parameters**:
- `fileDto` (form-data): The Excel file to validate
- `businessId` (query): The business ID GUID

**Response**:
```json
{
  "message": "Excel file validation successful. Messages can be sent within rate limit.",
  "success": true,
  "data": true
}
```

## How It Works

### 1. Rate Limit Retrieval
The method calls `GetRateLimitCountsAsync(businessId)` to get:
- `TotalLimit`: Maximum messages allowed per 24-hour period
- `ConsumeCount`: Messages already sent in the last 24 hours
- `AvailableCount`: Remaining messages that can be sent

### 2. Phone Number Extraction
The system processes the Excel file to extract unique phone numbers:

#### Column Detection
- Automatically detects phone number columns by checking headers for keywords:
  - "phone", "contact", "mobile", "number"
- If no matching headers found, assumes first column contains phone numbers

#### Phone Number Cleaning
- Removes common formatting characters: spaces, dashes, parentheses, dots
- Adds default country code (+91) if missing
- Validates minimum length (10 digits)
- Ensures uniqueness using case-insensitive comparison

### 3. Validation Logic
```csharp
return uniquePhoneNumbers.Count <= rateLimitCounts.AvailableCount;
```

## Usage Examples

### 1. Using the API Endpoint

```bash
curl -X POST "https://your-api-domain/api/campaigns/validate-excel-rate-limit?businessId=12345678-1234-1234-1234-123456789012" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -F "file=@campaign_contacts.xlsx"
```

### 2. Using the Service Method

```csharp
// In your controller or service
var fileDto = new UploadFileDto { File = uploadedFile };
var businessId = "12345678-1234-1234-1234-123456789012";

bool canSendMessages = await _campaignService.ValidateExcelFileAgainstRateLimitAsync(fileDto, businessId);

if (canSendMessages)
{
    // Proceed with campaign creation
    Console.WriteLine("Campaign can be processed within rate limits");
}
else
{
    // Handle rate limit exceeded
    Console.WriteLine("Too many contacts - exceeds available rate limit");
}
```

## Excel File Format

### Supported Formats
- `.xlsx` (Excel 2007+)
- `.xls` (Excel 97-2003)

### Expected Structure
```
| Phone Number | Name     | Email              |
|-------------|----------|--------------------|
| +919876543210| John Doe | <EMAIL>   |
| 9876543211  | Jane Doe | <EMAIL>   |
| (*************| Bob Smith| <EMAIL>   |
```

### Phone Number Formats Supported
- International format: `+919876543210`
- National format: `9876543210`
- Formatted: `(*************`, `************`
- With spaces: `************`

## Error Handling

### Common Errors
1. **File Not Provided**: Returns 400 Bad Request
2. **Invalid Excel Format**: Throws InvalidOperationException
3. **Rate Limit Service Unavailable**: Throws InvalidOperationException
4. **Empty Excel File**: Returns empty phone number set (validation passes)

### Error Response Example
```json
{
  "success": false,
  "message": "Error validating Excel file against rate limit: File format is invalid",
  "errors": "Stack trace details..."
}
```

## Rate Limiting Logic

The rate limiting is based on the WhatsApp Business API's 24-hour rolling window:

1. **Tier-based Limits**: Retrieved from `BusinessDetailsMetas.Tier` column
   - Format: "TIER_1K", "TIER_10K", etc.
   - Converted to numeric limits (1000, 10000, etc.)

2. **24-Hour Window**: Counts unique customers messaged in the last 24 hours
   - Only successfully sent messages (Status = Sent) are counted
   - Uses stored procedure `GetConsumeAndAvailableCount`

3. **Available Count Calculation**: `TotalLimit - ConsumeCount = AvailableCount`

## Integration Points

### Dependencies
- `ExcelPackage` (EPPlus): For Excel file processing
- `GetRateLimitCountsAsync`: Existing rate limit service
- `BusinessDetailsMetas`: For tier-based rate limits
- Stored Procedure: `GetConsumeAndAvailableCount`

### Related Components
- `CampaignExcelUploadedFile`: Existing Excel upload method
- `CountContactsInExcelFile`: Similar Excel processing logic
- Rate limiting system: WhatsApp Business API compliance

## Testing

### Test Scenarios
1. **Valid Excel with contacts under limit**: Should return `true`
2. **Valid Excel with contacts over limit**: Should return `false`
3. **Empty Excel file**: Should return `true`
4. **Invalid file format**: Should throw exception
5. **Missing business ID**: Should throw exception

### Sample Test Data
Create an Excel file with varying numbers of unique phone numbers to test different scenarios against your business's rate limit.
