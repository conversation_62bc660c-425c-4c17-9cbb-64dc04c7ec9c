namespace EngagetoEntities.Dtos.CampaignDtos
{
    public class RateLimitCountDto
    {
       //public string BusinessId { get; set; } = default!;
       // public long MessageLimit { get; set; }
        public long MetaTotalLimit { get; set; }
        public int TotalLimit { get; set; } 
        public int AdhocLimit { get; set; }
        public int ConsumeCount { get; set; }
        public int AvailableCount { get; set; }

       //public decimal UsagePercentage { get; set; }
    }
}
