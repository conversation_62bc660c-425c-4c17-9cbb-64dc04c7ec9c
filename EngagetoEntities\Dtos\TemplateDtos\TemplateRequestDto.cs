﻿using EngagetoEntities.Entities;

namespace EngagetoEntities.Dtos.TemplateDtos
{
    public class TemplateRequestDto
    {
        public string TemplateName { get; set; } = default!;
        public string? CountryCode { get; set; }
        public string Contact { get; set; } = default!;
        public string? Name { get; set; }
        public string? HeaderValue { get; set; }
        public List<string>? BodyVariableValues { get; set; } = new List<string>();
        public string? UserNumber { get; set; }  // New field
    }

    public class ButtonDto
    {
        public int Index { get; set; }
        public List<string> Values { get; set; } = new();
    }

    public class ProcessTemplateParameters
    {
        public Template? Template { get; set; }
        public List<string> Variables { get; set; } = new();
        public string BusinessId { get; set; } = string.Empty;
        public string PhoneNumber { get; set; } = string.Empty;
    }
    public class TemplateJobData
    {
        public string MethodName { get; set; } = string.Empty;
        public object Parameters { get; set; } = new();
    }
}
