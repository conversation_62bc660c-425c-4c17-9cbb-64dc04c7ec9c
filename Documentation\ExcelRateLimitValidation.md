# Excel File Rate Limit Validation

## Overview

This feature validates uploaded Excel files containing WhatsApp campaign data against the Meta WhatsApp Business API rolling 24-hour rate limits. The system extracts unique phone numbers from the Excel file and checks if they can be messaged within the current rate limit window.

## Implementation Details

### Integration with Existing Upload Method

**Location**: `EngagetoRepository/Services/CampaignService.cs`

**Method**: `CampaignExcelUploadedFile(UploadFileDto fileDto)`

The rate limit validation is now integrated into the existing Excel upload method. The system performs two validations:

1. **Wallet Balance Validation** (existing): Checks if wallet balance is sufficient
2. **WhatsApp Rate Limit Validation** (new): Checks if contacts can be messaged within 24-hour rolling window

Both validations must pass for the upload to succeed.

### Core Validation Method: `ValidateExcelFileAgainstRateLimitAsync`

**Purpose**: Checks if unique phone numbers from Excel can be sent within the Meta WhatsApp Business API rolling 24-hour window.

**Parameters**:

- `fileDto` (UploadFileDto): The uploaded Excel file containing campaign data
- `businessId` (string): The business ID to check rate limits for

**Returns**: `Task<bool>` - True if messages can be sent within rate limit; otherwise, false

### API Endpoint (Modified)

**Endpoint**: `POST /api/campaigns/CampaignUploadExcelFile`

**Parameters**:

- `fileDto` (form-data): The Excel file to upload

**Response** (Success):

```json
{
  "success": true,
  "message": "File uploaded successfully. Contact count: 150",
  "data": {
    "uploadResult": { ... },
    "contactCount": 150
  }
}
```

**Response** (Failure):

```json
{
  "success": false,
  "message": "File upload failed. This could be due to insufficient wallet balance, contact limit exceeded, or WhatsApp rate limit exceeded (too many contacts messaged in last 24 hours). Contact count: 150",
  "data": {
    "success": false,
    "contactCount": 150
  }
}
```

## How It Works

### 1. Meta WhatsApp Business API Rolling 24-Hour Window

The Meta WhatsApp Business API enforces rate limits based on a **rolling 24-hour window**:

- **Rate Limit Basis**: Unique customers messaged, not total messages
- **Rolling Window**: Exactly 24 hours from the current time (not calendar day)
- **Counting Logic**: Only successfully sent messages (Status = Sent) count toward the limit
- **Tier-Based Limits**: Different businesses have different limits (TIER_1K = 1000, TIER_10K = 10000, etc.)

### 2. Rate Limit Retrieval

The method calls `GetRateLimitCountsAsync(businessId)` to get:

- `TotalLimit`: Maximum unique customers allowed per 24-hour period (from business tier)
- `ConsumeCount`: Unique customers already messaged in the last 24 hours
- `AvailableCount`: Remaining unique customers that can be messaged

### 3. Phone Number Extraction

The system processes the Excel file to extract unique phone numbers:

#### Column Detection

- Automatically detects phone number columns by checking headers for keywords:
  - "phone", "contact", "mobile", "number"
- If no matching headers found, assumes first column contains phone numbers

#### Phone Number Cleaning

- Removes common formatting characters: spaces, dashes, parentheses, dots
- Adds default country code (+91) if missing
- Validates minimum length (10 digits)
- Ensures uniqueness using case-insensitive comparison

### 4. Rolling Window Validation Logic

The key innovation is checking which contacts from the Excel file have **NOT** been messaged in the last 24 hours:

```csharp
// Step 1: Get contacts from Excel file
var uniquePhoneNumbers = await ExtractUniquePhoneNumbersFromExcel(fileDto.File);

// Step 2: Check which contacts have NOT been messaged in last 24 hours
var newContactsCount = await CountNewContactsNotInRollingWindow(uniquePhoneNumbers, businessId);

// Step 3: Validate against available rate limit
return newContactsCount <= rateLimitCounts.AvailableCount;
```

#### Rolling Window Query Logic:

```sql
-- Find contacts that HAVE been messaged in last 24 hours
SELECT DISTINCT REPLACE([To], '+', '')
FROM Conversations
WHERE [From] = @BusinessId
  AND [Status] = 'Sent'
  AND CreatedAt >= @TwentyFourHoursAgo
  AND REPLACE([To], '+', '') IN @ExcelPhoneNumbers

-- Count contacts that have NOT been messaged (new contacts for rate limiting)
-- These are the ones that will count against the rate limit
```

## Usage Examples

### 1. Using the API Endpoint

```bash
curl -X POST "https://your-api-domain/api/campaigns/validate-excel-rate-limit?businessId=12345678-1234-1234-1234-123456789012" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -F "file=@campaign_contacts.xlsx"
```

### 2. Using the Service Method

```csharp
// In your controller or service
var fileDto = new UploadFileDto { File = uploadedFile };
var businessId = "12345678-1234-1234-1234-123456789012";

bool canSendMessages = await _campaignService.ValidateExcelFileAgainstRateLimitAsync(fileDto, businessId);

if (canSendMessages)
{
    // Proceed with campaign creation
    Console.WriteLine("Campaign can be processed within rate limits");
}
else
{
    // Handle rate limit exceeded
    Console.WriteLine("Too many contacts - exceeds available rate limit");
}
```

## Excel File Format

### Supported Formats

- `.xlsx` (Excel 2007+)
- `.xls` (Excel 97-2003)

### Expected Structure

```
| Phone Number | Name     | Email              |
|-------------|----------|--------------------|
| +919876543210| John Doe | <EMAIL>   |
| 9876543211  | Jane Doe | <EMAIL>   |
| (*************| Bob Smith| <EMAIL>   |
```

### Phone Number Formats Supported

- International format: `+919876543210`
- National format: `9876543210`
- Formatted: `(*************`, `************`
- With spaces: `************`

## Error Handling

### Common Errors

1. **File Not Provided**: Returns 400 Bad Request
2. **Invalid Excel Format**: Throws InvalidOperationException
3. **Rate Limit Service Unavailable**: Throws InvalidOperationException
4. **Empty Excel File**: Returns empty phone number set (validation passes)

### Error Response Example

```json
{
  "success": false,
  "message": "Error validating Excel file against rate limit: File format is invalid",
  "errors": "Stack trace details..."
}
```

## Rate Limiting Logic

The rate limiting is based on the WhatsApp Business API's 24-hour rolling window:

1. **Tier-based Limits**: Retrieved from `BusinessDetailsMetas.Tier` column

   - Format: "TIER_1K", "TIER_10K", etc.
   - Converted to numeric limits (1000, 10000, etc.)

2. **24-Hour Window**: Counts unique customers messaged in the last 24 hours

   - Only successfully sent messages (Status = Sent) are counted
   - Uses stored procedure `GetConsumeAndAvailableCount`

3. **Available Count Calculation**: `TotalLimit - ConsumeCount = AvailableCount`

## Integration Points

### Dependencies

- `ExcelPackage` (EPPlus): For Excel file processing
- `GetRateLimitCountsAsync`: Existing rate limit service
- `BusinessDetailsMetas`: For tier-based rate limits
- Stored Procedure: `GetConsumeAndAvailableCount`

### Related Components

- `CampaignExcelUploadedFile`: Existing Excel upload method
- `CountContactsInExcelFile`: Similar Excel processing logic
- Rate limiting system: WhatsApp Business API compliance

## Testing

### Test Scenarios

1. **Valid Excel with contacts under limit**: Should return `true`
2. **Valid Excel with contacts over limit**: Should return `false`
3. **Empty Excel file**: Should return `true`
4. **Invalid file format**: Should throw exception
5. **Missing business ID**: Should throw exception

### Sample Test Data

Create an Excel file with varying numbers of unique phone numbers to test different scenarios against your business's rate limit.
