﻿using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Spreadsheet;
using EngagetoBackGroundJobs.Implementation;
using EngagetoBackGroundJobs.Interfaces;
using EngagetoContracts.CampaignContracts;
using EngagetoContracts.ContactContracts;
using EngagetoContracts.GeneralContracts;
using EngagetoContracts.Services;
using EngagetoContracts.UserContracts;
using EngagetoDapper.Data.Dapper.Services.FilterServices;
using EngagetoDapper.Data.Dtos;
using EngagetoDapper.Data.Interfaces.CampaignInterfaces.ICampaignRepositories;
using EngagetoDapper.Data.Interfaces.GenericInterfaces;
using EngagetoDapper.Data.Interfaces.ILogInterfaces;
using EngagetoDapper.Data.Connections;
using EngagetoEntities;
using EngagetoEntities.DdContext;
using EngagetoEntities.Dtos.AutomationDtos;
using EngagetoEntities.Dtos.CampaignDto;
using EngagetoEntities.Dtos.CampaignDtos;
using EngagetoEntities.Dtos.CommonDtos;
using EngagetoEntities.Dtos.FilterDtos;
using EngagetoEntities.Dtos.TemplateDtos;
using EngagetoEntities.Entities;
using EngagetoEntities.Enums;
using EngagetoEntities.UserEntities.Dtos;
using EngagetoEntities.Utilities;
using Humanizer;
using Mapster;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Microsoft.CodeAnalysis.CSharp.Syntax;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Diagnostics;
using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;
using Newtonsoft.Json;
using Dapper;
using OfficeOpenXml;
using System.Data;
using System.Linq.Expressions;
using System.Reflection;
using System.Text;
using System.Text.Json;
using DocumentFormat.OpenXml.Wordprocessing;

namespace EngagetoRepository.Services
{
    public class CampaignService : ICampaignService
    {
        private readonly EngagetoDapper.Data.Dapper.Repositories.InboxRepositories.IInboxRepository _inboxRepository;
        private readonly IGenericRepository _genericRepository;
        private readonly ICampaignScheduler _campaignScheduler;
        private readonly ICampaignRespository _campaignRespository;
        private readonly IContactRepositoryBase _contactRepository;
        private readonly IFilterService _filterService;
        private readonly ApplicationDbContext _appDbContext;
        private readonly ILogHistoryService _logHistoryService;
        private readonly IBlobStorageService _blobStorageService;
        private readonly IUserIdentityService _userIdentityService;
        private ApplicationDbContext _campaignDbContext;
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly IConfiguration _configuration;
        private readonly string _schedulerJoburl;
        private readonly string _terminateJobUrl;
        private readonly IEnvironmentService _environmentService;
        private readonly IWalletService _walletService;
        private readonly ISqlConnectionFactory _connectionFactory;


        public CampaignService(IGenericRepository genericRepository,
            EngagetoDapper.Data.Dapper.Repositories.InboxRepositories.IInboxRepository inboxRepository,
            ICampaignScheduler campaignScheduler, ILogHistoryService logHistoryService,
            ApplicationDbContext appDbContext,
            IFilterService filterService,
            ICampaignRespository campaignRespository, IBlobStorageService blobStorageService, IUserIdentityService userIdentityService,
            ApplicationDbContext campaignDbContext,
            IHttpClientFactory httpClientFactory,
            IConfiguration configuration,
            IEnvironmentService environmentService,
            IContactRepositoryBase contactRepositoryBase,
            IWalletService walletService,
            ISqlConnectionFactory connectionFactory
            )
        {
            _genericRepository = genericRepository;
            _inboxRepository = inboxRepository;
            _appDbContext = appDbContext;
            _logHistoryService = logHistoryService;
            _campaignScheduler = campaignScheduler;
            _filterService = filterService;
            _campaignRespository = campaignRespository;
            _blobStorageService = blobStorageService;
            _userIdentityService = userIdentityService;
            _campaignDbContext = campaignDbContext;
            _configuration = configuration;
            _httpClientFactory = httpClientFactory;
            _environmentService = environmentService;
            _contactRepository = contactRepositoryBase;
            _walletService = walletService;
            _connectionFactory = connectionFactory;


            if (_environmentService.IsDevelopment)
            {
                _schedulerJoburl = _configuration["FunctionSettings:Dev_ScheduleJobUrl"] ?? "";
                _terminateJobUrl = _configuration["FunctionSettings:Dev_TerminateJobUrl"] ?? "";
            }
            else
            {
                _schedulerJoburl = _configuration["FunctionSettings:Prod_ScheduleJobUrl"] ?? "";
                _terminateJobUrl = _configuration["FunctionSettings:Prod_TerminateJobUrl"] ?? "";
            }
        }


        public async Task<bool> CreateAsync(Guid userId, string userName, CreateCampaignDto dto)
        {
            string jobId = string.Empty;
            if (string.IsNullOrEmpty(dto.Name))
            {
                throw new InvalidDataException("Campaign name cannot be empty.");
            }
            try
            {
                if (!(await IsExistCampaignNameAsync(dto.BusinessId, dto.Name)) || (dto.Id != null && dto.Id != Guid.Empty && dto?.ScheduleDate != null && dto.ScheduleDate != DateTime.MinValue))
                {
                    var campaign = (await _genericRepository.GetByObjectAsync<Campaign>(new Dictionary<string, object> { { "CampaignId", dto.Id ?? Guid.Empty } }, "Campaigns")).FirstOrDefault();
                    if (campaign != null)
                    {
                        if (!string.IsNullOrEmpty(campaign.ScheduleJobId))
                        {
                            var functionUrl = _terminateJobUrl;
                            var terminateRequestBody = new { JobId = campaign.ScheduleJobId };
                            await CallFunctionAsync(terminateRequestBody, functionUrl ?? string.Empty);

                            await _genericRepository.DeleteRecordAsync<CampaignTracker>(nameof(CampaignTracker), new()
                            {
                                new(nameof(CampaignTracker.CampaignId),campaign.CampaignId,"="),
                                new(nameof(CampaignTracker.BusinessId),campaign.BusinessId,"=")
                            });
                        }

                        if (!string.IsNullOrEmpty(campaign.ChildScheduleJobIds))
                        {
                            var jobIds = campaign.ChildScheduleJobIds
                                .Split(',', StringSplitOptions.RemoveEmptyEntries)
                                .Select(id => id.Trim())
                                .Where(id => !string.IsNullOrEmpty(id));
                            foreach (var job in jobIds)
                            {
                                var functionUrl = _terminateJobUrl;
                                var terminateRequestBody = new { job = job };
                                await CallFunctionAsync(terminateRequestBody, functionUrl ?? string.Empty);
                            }
                        }
                    }
                    else
                    {
                        campaign = new();
                        campaign.CampaignId = Guid.NewGuid();
                        campaign.CampaignTitle = dto.Name;
                        campaign.UserId = _userIdentityService.UserId;
                        campaign.BusinessId = _userIdentityService.BusinessId;
                        campaign.Createdby = _userIdentityService.Name;
                        campaign.CreatedDate = DateTime.UtcNow;
                    }
                    campaign.UploadedFileId = dto.UploadedFileId;
                    var audienceList = dto.Audiences?.Select(audience => audience.ToString()).ToList();
                    campaign.Audiance = audienceList != null && audienceList.Count() > 1000 ? string.Join(",", audienceList) : null;
                    campaign.SendTextType = dto.Text;
                    campaign.MediaUrl = dto.MediaUrl;
                    campaign.TemplateId = dto.Template?.Id;
                    campaign.DateSetLive = dto.ScheduleDate;
                    campaign.HeaderValue = dto.Template?.HeaderValue != null
                        ? ((string.IsNullOrEmpty(dto.Template.HeaderValue.Value) || StringHelper.IsLeadratVariable(dto.Template.HeaderValue.Value))
                        ? dto.Template.HeaderValue.FallbackValue
                        : dto.Template.HeaderValue.Value) : null;
                    campaign.BodyValues = dto.Template?.BodyValues != null ? string.Join(",", dto.Template.BodyValues.Select(i => ((string.IsNullOrEmpty(i.Value) || StringHelper.IsLeadratVariable(i.Value))) ? i.FallbackValue : i.Value)) : null;
                    campaign.EditedDate = DateTime.UtcNow;
                    campaign.Editedby = userName;

                    var carouselVariables = new List<CarouselCardVariableDto>();
                    if (dto.Template != null && dto.Template.CarouselVariables != null)
                    {
                        foreach (var carousel in dto.Template.CarouselVariables)
                        {
                            var bodyVariables = carousel.BodyCarouselVariableValues != null
                                ? carousel.BodyCarouselVariableValues.Select(i => string.IsNullOrEmpty(i.Value) || StringHelper.IsLeadratVariable(i.Value) ? i.FallbackValue : i.Value).ToList()
                                : new();

                            var redirectUrlVariables = carousel.RedirectUrlVariableValues != null
                                ? carousel.RedirectUrlVariableValues.Select(i => string.IsNullOrEmpty(i.Value) || StringHelper.IsLeadratVariable(i.Value) ? i.FallbackValue : i.Value).ToList()
                                : new();

                            var carouselDto = new CarouselCardVariableDto
                            {
                                BodyCarouselVariableValues = bodyVariables.ToArray(),
                                RedirectUrlVariableValues = redirectUrlVariables.ToArray(),
                                MediaUrl = null
                            };
                            if (carouselDto != null)
                            {
                                carouselVariables.Add(carouselDto);
                            }
                        }
                    }
                    campaign.CarouselVariables = JsonConvert.SerializeObject(carouselVariables);

                    var columns = StringHelper.GetPropertyNames<Campaign>(false);
                    var result = false;
                    if (dto?.ScheduleDate != null && dto.ScheduleDate != DateTime.MinValue && campaign.DateSetLive.Value > DateTime.UtcNow)
                    {
                        await SaveCampaignTrackersAsync(campaign.CampaignId, campaign.BusinessId, campaign.UserId, audienceList ?? new());
                        campaign.State = CampaignState.Scheduled;
                        var input = new
                        {
                            id = campaign.CampaignId,
                            JsonData = JsonConvert.SerializeObject(campaign),
                            type = "Campaign",
                            scheduledTime = campaign.DateSetLive,
                            isDevelopment = _environmentService.IsDevelopment
                        };
                        var json = JsonConvert.SerializeObject(input);
                        var functionUrl = _schedulerJoburl;
                        campaign.State = CampaignState.Scheduled;
                        var response = await CallFunctionAsync(input, functionUrl ?? string.Empty);
                        using var jsonDoc = JsonDocument.Parse(response);
                        var scheduledJobId = jsonDoc.RootElement.GetProperty("Id").GetString();
                        if ((dto.Id != null && dto.Id != Guid.Empty))
                        {
                            campaign.Editedby = _userIdentityService.Name;
                            campaign.EditedDate = DateTime.UtcNow;
                            campaign.ScheduleJobId = scheduledJobId;
                            result = await _genericRepository.UpdateRecordAsync<Campaign>("Campaigns", StringHelper.GetPropertyNames<Campaign>(false), campaign, new() { { "CampaignId", dto.Id } });
                        }
                        else
                        {
                            result = await _genericRepository.InsertRecordsAsync("Campaigns", columns, new List<Campaign> { campaign });
                        }
                    }
                    else
                    {
                        await SaveCampaignTrackersAsync(campaign.CampaignId, campaign.BusinessId, campaign.UserId, audienceList ?? new());
                        campaign.State = CampaignState.Processing;
                        var input = new
                        {
                            id = campaign.CampaignId,
                            JsonData = JsonConvert.SerializeObject(campaign),
                            type = "Campaign",
                            scheduledTime = DateTime.UtcNow,
                            isDevelopment = _environmentService.IsDevelopment
                        };
                        var json = JsonConvert.SerializeObject(input);
                        var functionUrl = _schedulerJoburl;
                        var response = await CallFunctionAsync(input, functionUrl ?? string.Empty);
                        campaign.DateSetLive = DateTime.UtcNow;
                        using var jsonDoc = JsonDocument.Parse(response);
                        var scheduledJobId = jsonDoc.RootElement.GetProperty("Id").GetString();
                        campaign.ScheduleJobId = scheduledJobId;
                        result = await _genericRepository.InsertRecordsAsync("Campaigns", columns, new List<Campaign> { campaign });
                    }
                    return result;
                }
                throw new Exception("Campaign name already exists.");
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        public async Task<bool> IsExistCampaignNameAsync(string businessId, string name)
        {
            try
            {
                if (await _genericRepository.IsExistAsync<Campaign>(new Dictionary<string, object> { { "CampaignTitle", name.Trim() }, { "BusinessId", businessId } }, "Campaigns"))
                {
                    return true;
                }
                else
                {
                    return false;
                }
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        public async Task SaveCampaignTrackersAsync(Guid campaignId, string businessId, Guid userId, List<string> contactIds)
        {
            try
            {
                if (contactIds?.Any() == true)
                {
                    const int batchSize = 1000;
                    var campaignTrackers = contactIds.Select(id => new CampaignTracker
                    {
                        Id = Guid.NewGuid(),
                        BusinessId = businessId,
                        CampaignId = campaignId,
                        Status = "Pending",
                        UserId = userId,
                        ContactId = id,
                        CreatedAt = DateTime.UtcNow,
                        CreatedBy = userId,
                        UpdatedAt = DateTime.UtcNow,
                        UpdatedBy = userId
                    });
                    int count = campaignTrackers.Count();
                    // Batching logic
                    for (int i = 0; i < count; i += batchSize)
                    {
                        var batch = campaignTrackers.Skip(i).Take(batchSize).ToList();
                        await _genericRepository.InsertRecordsAsync<CampaignTracker>
                            (
                                nameof(CampaignTracker),
                                StringHelper.GetPropertyNames<CampaignTracker>(),
                                batch
                            );
                    }
                }
            }
            catch (Exception ex)
            {
                // Log the exception properly (not just throw)
                throw new Exception("Error saving campaign trackers in batch.", ex);
            }
        }


        public async Task<bool> RerunCampaignAsync(Guid userId, string userName, BaseCampaignsDto campaignDto, DateTime? scheduledDate)
        {
            try
            {
                if (!(await IsExistCampaignNameAsync(campaignDto.BusinessId, campaignDto.Name)))
                {
                    var existCampaign = (await _genericRepository.GetByObjectAsync<Campaign>(new Dictionary<string, object> { { "CampaignId", campaignDto.Id ?? Guid.Empty } }, "Campaigns")).FirstOrDefault();
                    if (existCampaign == null || existCampaign.CampaignId == Guid.Empty || existCampaign.State == CampaignState.Incompleted)
                        throw new Exception("Campaign does not exist with the given campaign id.");


                    DateTime scheduledDatetime = (DateTime)(scheduledDate != null ? scheduledDate : DateTime.UtcNow);
                    await RerunCampaignBackgroundAsync(userId, userName, campaignDto, scheduledDate, existCampaign ?? new());
                    return true;
                }
                throw new Exception("Campaign with this name is already exist.");
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }
        public async Task<bool> RerunCampaignBackgroundAsync(Guid userId, string userName, BaseCampaignsDto campaignDto, DateTime? scheduledDate, Campaign campaign)
        {
            try
            {
                const int batchSize = 1000;
                var newCampaign = campaign.Adapt<Campaign>();
                newCampaign.CampaignId = Guid.NewGuid();
                int totalCount = await _genericRepository.Count<CampaignTracker>(new()
                {
                    { nameof(CampaignTracker.CampaignId), campaign.CampaignId },
                    { nameof(CampaignTracker.ConvStatus), ConvStatus.failed }
                });
                #region Save Campaign 
                for (int i = 0; i < totalCount; i += batchSize)
                {
                    var failedContactIds = await _genericRepository.GetColumnValuesAsync<string>
                     (
                        new()
                        {
                            new(nameof(CampaignTracker.CampaignId), campaign.CampaignId, "="),
                            new(nameof(CampaignTracker.ConvStatus), (int)ConvStatus.failed, "=")
                        },
                        tableName: nameof(CampaignTracker),
                        columnName: nameof(CampaignTracker.ContactId),
                        orderByColumn: nameof(CampaignTracker.CreatedAt),
                        orderDirection: "ASC",
                        page: i,
                        pageSize: batchSize
                     );

                    await SaveCampaignTrackersAsync(newCampaign.CampaignId, campaign.BusinessId, _userIdentityService.UserId, failedContactIds);
                }
                #endregion
                newCampaign.UserId = userId;
                newCampaign.CreatedDate = DateTime.UtcNow;
                newCampaign.CampaignTitle = campaignDto.Name ?? string.Empty;
                newCampaign.State = CampaignState.Scheduled;
                newCampaign.UploadedFileId = 0;
                newCampaign.DateSetLive = (scheduledDate != null && scheduledDate >= DateTime.UtcNow) ? scheduledDate : DateTime.UtcNow;
                var columns = StringHelper.GetPropertyNames<Campaign>(false);
                var input = new
                {
                    id = newCampaign.CampaignId,
                    JsonData = JsonConvert.SerializeObject(newCampaign),
                    type = "Campaign",
                    scheduledTime = newCampaign.DateSetLive,
                    isDevelopment = _environmentService.IsDevelopment
                };
                var functionUrl = _schedulerJoburl;

                if (scheduledDate == null)
                {
                    newCampaign.State = CampaignState.Processing;
                }
                var response = await CallFunctionAsync(input, functionUrl ?? string.Empty);
                using var jsonDoc = JsonDocument.Parse(response);
                var scheduledJobId = jsonDoc.RootElement.GetProperty("Id").GetString();
                newCampaign.ScheduleJobId = scheduledJobId;
                newCampaign.Editedby = userName;
                newCampaign.EditedDate = DateTime.UtcNow;
                var result = await _genericRepository.InsertRecordsAsync("Campaigns", columns, new List<Campaign> { newCampaign });
                return result;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        private async Task<string> CallFunctionAsync<T>(T requestBody, string functionUrl)
        {
            if (string.IsNullOrEmpty(functionUrl))
            {
                throw new InvalidOperationException("Function URL is not configured.");
            }
            var client = _httpClientFactory.CreateClient();
            var jsonPayload = System.Text.Json.JsonSerializer.Serialize(requestBody);
            var content = new StringContent(jsonPayload, Encoding.UTF8, "application/json");
            var response = await client.PostAsync(functionUrl, content);
            var result = await response.Content.ReadAsStringAsync();
            Console.WriteLine(result);
            return result;
        }

        public async Task<bool> DeleteAsync(string businessId, Guid id)
        {
            try
            {
                var campain = await _genericRepository.GetByObjectAsync<Campaign>(new Dictionary<string, object> { { "BusinessId", businessId }, { "CampaignId", id } }, "Campaigns");
                if (campain == null)
                    return true;

                if (!string.IsNullOrEmpty(campain.FirstOrDefault()?.ScheduleJobId))
                {
                    // _jobService.Delete(campain.FirstOrDefault()?.ScheduleJobId ?? "");

                    var functionUrl = _terminateJobUrl;

                    var terminateRequestBody = new { JobId = campain.FirstOrDefault()?.ScheduleJobId ?? "" };
                    await CallFunctionAsync(terminateRequestBody, functionUrl ?? string.Empty);
                }
                return await _genericRepository.DeleteRecordAsync<Campaign>("Campaigns", new List<RequestFilterDto>()
                     {
                       new RequestFilterDto()
                       {
                            Value = businessId,
                            Key = "BusinessId"
                       },
                       new RequestFilterDto()
                       {
                            Value = id,
                            Key = "CampaignId"
                       }
                    });
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        public async Task<Campaign> GetCampaignAsync(string businessId, Guid campaignId)
        {

            try
            {
                var campain = await _appDbContext.Campaigns.FirstOrDefaultAsync(i => i.BusinessId == businessId && i.CampaignId == campaignId);
                return campain ?? new();
            }
            catch (Exception ex)
            {
                throw;
            }


        }

        public async Task<List<CampaignMessageCountsDto>> GetCampaignAnalyticsAsync(string businessId, Guid? userId, FilterDto? filter, int? page, int? pageSize)
        {
            try
            {
                bool isFilterData = false;
                if (filter == null || filter?.Sorting == null)
                {
                    filter.Sorting = new() { Column = "CreatedDate", Order = "desc" };
                }
                if (filter?.Filtering == null)
                {
                    filter.Filtering = new FilterGroup() { FilterType = "and" };
                    filter.Filtering.Conditions = new() { new() { Column = "State", Operator = "=", Value = ((int)CampaignState.Completed).ToString() } };
                    isFilterData = true;
                }
                var campaigns = await _filterService.FilterDataAsync<Campaign>(businessId, null, filter, "Campaigns", page, pageSize);
                if (isFilterData)
                {
                    campaigns = campaigns.Where(x => x.State == CampaignState.Completed);
                }
                var waIds = campaigns
                    .Where(x => !string.IsNullOrEmpty(x.WhatsAppMessagesId))
                    .GroupBy(x => x.CampaignId)
                    .ToDictionary(
                        group => group.Key,
                        group => group
                            .SelectMany(x => x.WhatsAppMessagesId.Split(','))
                            .Where(id => !string.IsNullOrEmpty(id))
                            .ToList()
                    );

                var campaignAnalytics = await _inboxRepository.GetCampaignAnalyticsCountAsync(businessId, waIds, CancellationToken.None);

                var joinedCampaigns = campaigns.GroupJoin(
                        campaignAnalytics,
                        campaign => campaign.CampaignId,
                        analytics => analytics.Key,
                        (campaign, analyticsGroup) =>
                        {
                            var analyticsValues = analyticsGroup.SelectMany(a => a.Value);
                            var sentCount = analyticsValues.Sum(v => v.AttemptedCount);
                            var deliveredCount = analyticsValues.Sum(v => v.DeliveredCount);
                            var failedCount = analyticsValues.Sum(v => v.FailedCount ?? 0);
                            var repliedCount = analyticsValues.Sum(v => v.RepliedCount ?? 0);
                            var readCount = analyticsValues.Sum(v => v.ReadCount ?? 0);

                            return new CampaignMessageCountsDto
                            {
                                CampaignId = campaign.CampaignId.ToString(),
                                CampaignTitle = campaign.CampaignTitle,
                                Createdby = campaign.Createdby,
                                Attempted = campaign.WhatsAppMessagesId?.Split(",").Length,
                                Scheduled = campaign.Audiance?.Split(",").Length,
                                Sent = sentCount,
                                Delivered = deliveredCount,
                                Failed = failedCount,
                                Replied = repliedCount,
                                Read = readCount,
                                DateSetLive = campaign.DateSetLive,
                                State = campaign.State
                            };
                        }
                    ).ToList();
                joinedCampaigns.ForEach(x => x.Undelivered = x.Sent - (x.Delivered + x.Failed));

                // Apply any extra filtering if needed
                return ExtraFilterData(joinedCampaigns, filter);
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        public async Task<List<ViewCampaignAnalyticsDto>> GetAllCampaignAnalyticsAsync(string businessId, FilterDto? filter, int? page, int? pageSize)
        {
            try
            {

                if (filter == null || filter?.Sorting == null)
                {
                    filter.Sorting = new() { Column = "CreatedDate", Order = "desc" };
                }

                // Ensure Filtering and its Conditions list are initialized
                filter.Filtering ??= new FilterGroup { FilterType = "and", Conditions = new List<FilterCondition>() };

                // Remove any existing State conditions to ensure we only get Completed and Processing
                filter.Filtering.Conditions = filter.Filtering.Conditions!
                    .Where(c => !c.Column.Equals("State", StringComparison.OrdinalIgnoreCase))
                    .ToList();

                // Store existing conditions
                var existingConditions = filter.Filtering.Conditions.ToList();

                // Clear conditions and add State conditions with OR logic first
                filter.Filtering.Conditions.Clear();
                filter.Filtering.FilterType = "or";

                // Add State conditions
                filter.Filtering.Conditions.Add(new FilterCondition
                {
                    Column = "State",
                    Operator = "=",
                    Value = ((int)CampaignState.Completed).ToString()
                });

                filter.Filtering.Conditions.Add(new FilterCondition
                {
                    Column = "State",
                    Operator = "=",
                    Value = ((int)CampaignState.Processing).ToString()
                });

                // If there were existing conditions, we need to handle them differently
                // For now, let's keep it simple and only filter by State
                // The existing conditions will be ignored to ensure we only get Completed/Processing
                var campaigns = await _filterService.FilterDataAsync<Campaign>(businessId, null, filter, "Campaigns", page, pageSize);

                var viewCampaignAnalyticsDto = campaigns.Adapt<List<ViewCampaignAnalyticsDto>>();
                return viewCampaignAnalyticsDto;
            }
            catch (Exception ex)
            {
                throw;
            }

        }

        public async Task<CampaignAnalytsicDto> GetCampaignAnalyticsDetailsAsync(Guid CampaignId)
        {
            var businessId = _userIdentityService.BusinessId;
            var campaignAnalytics = await _inboxRepository.GetCampaignAnalyticsCountAsyncById(businessId, CampaignId, CancellationToken.None);
            campaignAnalytics.Undelivered = campaignAnalytics.AttemptedCount - (campaignAnalytics.DeliveredCount + campaignAnalytics.FailedCount);
            return campaignAnalytics;

        }

        public async Task<List<Campaign>> GetScheduleCampaignAsync(string businessId, Guid? userId, FilterDto? filter, int? page, int? pageSize)
        {
            try
            {
                if (filter == null || filter?.Sorting == null)
                {
                    filter.Sorting = new() { Column = "CreatedDate", Order = "desc" };
                }
                if (filter?.Filtering == null)
                {
                    filter.Filtering = new FilterGroup() { FilterType = "and" };
                    filter.Filtering.Conditions = new() { new() { Column = "State", Operator = "=", Value = ((int)CampaignState.Scheduled).ToString() } };
                }
                var campaigns = await _filterService.FilterDataAsync<Campaign>(businessId, null, filter, "Campaigns", page, pageSize);
                return campaigns.ToList();
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        public async Task<int> GetCampaignCountAsync(string businessId, CampaignState state, FilterCondition? condition, Search? search)
        {
            if (condition != null && !string.IsNullOrEmpty(condition.Value) && DateTime.TryParse(condition.Value, out var date))
            {
                switch (condition.Operator)
                {

                    case "last7days":
                        condition.Operator = ">";
                        condition.Value = date != DateTime.MinValue ? date.AddDays(-7).ToString("o") : DateTime.UtcNow.AddDays(-7).ToString("o");
                        break;
                    case "last14days":
                        condition.Operator = ">";
                        condition.Value = date != DateTime.MinValue ? date.AddDays(-14).ToString("o") : DateTime.UtcNow.AddDays(-14).ToString("o");
                        break;
                    case "last30days":
                        condition.Operator = ">";
                        condition.Value = date != DateTime.MinValue ? date.AddDays(-30).ToString("o") : DateTime.UtcNow.AddDays(-30).ToString("o");
                        break;
                    case "all":
                        condition.Operator = "<";
                        condition.Value = date != DateTime.MinValue ? date.AddDays(1).ToString("o") : DateTime.UtcNow.AddDays(1).ToString("o");
                        break;
                    case "next7days":
                        condition.Operator = "<";
                        condition.Value = date != DateTime.MinValue ? date.AddDays(7).ToString("o") : DateTime.UtcNow.AddDays(7).ToString("o");
                        break;
                    case "next14days":
                        condition.Operator = "<";
                        condition.Value = date != DateTime.MinValue ? date.AddDays(14).ToString("o") : DateTime.UtcNow.AddDays(14).ToString("o");
                        break;
                    case "next30days":
                        condition.Operator = "<";
                        condition.Value = date != DateTime.MinValue ? date.AddDays(30).ToString("o") : DateTime.UtcNow.AddDays(30).ToString("o");
                        break;
                    case "allschedule":
                        condition.Operator = ">";
                        condition.Value = date != DateTime.MinValue ? date.ToString("o") : DateTime.UtcNow.ToString("o");
                        break;

                }
            }

            return await _campaignRespository.GetCampaignCountAsync(businessId, state, condition, search);
        }

        public async Task<DataTable> GetCampaignExportAsync(string businessId, Guid userId, Guid campaignId)
        {
            if (_userIdentityService.BusinessId != businessId && _userIdentityService.UserId != userId)
                throw new UnauthorizedAccessException("Invalid business id or user id");

            // Fetch completed campaigns
            var completedCampaigns = await _campaignDbContext.Campaigns
                .Where(c => c.State == CampaignState.Completed && c.BusinessId == businessId && c.CampaignId == campaignId)
                .ToListAsync();

            if (!completedCampaigns.Any())
                return new DataTable();

            // Extract WhatsAppMessageIds
            var campaignMessageIds = completedCampaigns
                .Where(c => !string.IsNullOrEmpty(c.WhatsAppMessagesId))
                .SelectMany(c => c.WhatsAppMessagesId.Split(',', StringSplitOptions.RemoveEmptyEntries))
                .Distinct()
                .ToList();

            if (!campaignMessageIds.Any())
                return new DataTable();

            // Fetch conversations
            var conversations = await _campaignDbContext.Conversations
                .Where(convo => campaignMessageIds.Contains(convo.WhatsAppMessageId))
                .ToListAsync();

            // Extract contact numbers and concatenate them (client-side)       
            var contactIds = conversations
                  .Select(c => c.ContactId)
                  .Distinct()
                  .ToList();

            // Fetch contacts from the database (client-side processing of concatenation and distinct)
            var contacts = await _campaignDbContext.Contacts
                 .Where(contact => contactIds.Contains(contact.ContactId))
                 .ToListAsync();

            var contactDictionary = contacts
                .DistinctBy(contact => string.Concat(contact.CountryCode, contact.Contact).Replace("+", ""))
                .ToDictionary(contact => string.Concat(contact.CountryCode, contact.Contact).Replace("+", ""), contact => contact);

            // Pre-fetch template names
            var templateNames = await _campaignDbContext.Templates
                .Where(t => completedCampaigns.Select(c => c.TemplateId).Contains(t.TemplateId))
                .ToDictionaryAsync(t => t.TemplateId, t => t.TemplateName);

            // Create the DataTable
            var dataTable = new DataTable();
            dataTable.Columns.Add("Campaign Title");
            dataTable.Columns.Add("CreatedBy");
            dataTable.Columns.Add("Campaign State");
            dataTable.Columns.Add("Campaign LiveDate");
            dataTable.Columns.Add("TemplateName");
            dataTable.Columns.Add("Status");
            dataTable.Columns.Add("MessageType");
            dataTable.Columns.Add("Contact Name");
            dataTable.Columns.Add("Contact Number");
            dataTable.Columns.Add("Error");

            // Populate DataTable
            foreach (var campaign in completedCampaigns)
            {
                var relatedConversations = conversations
                    .Where(convo => !string.IsNullOrEmpty(campaign.WhatsAppMessagesId) && campaign.WhatsAppMessagesId.Contains(convo.WhatsAppMessageId))
                    .ToList();

                var templateName = templateNames.TryGetValue(campaign.TemplateId ?? Guid.Empty, out var name) ? name : "";

                foreach (var conversation in relatedConversations)
                {
                    var contact = contactDictionary.TryGetValue(conversation.To.Replace("+", ""), out var contactDetails) ? contactDetails : null;

                    dataTable.Rows.Add(
                        campaign.CampaignTitle,
                        campaign.Createdby,
                        campaign.State.ToString(),
                        campaign.DateSetLive,
                        templateName,
                        conversation.Status,
                        conversation.MessageType,
                        contact?.Name ?? "",
                        string.Concat(contact?.CountryCode, contact?.Contact) ?? conversation.To,
                        conversation.ErrorMessage
                    );
                }
            }
            return dataTable;
        }

        #region Extra Filter
        private List<CampaignMessageCountsDto> ExtraFilterData(List<CampaignMessageCountsDto> data, FilterDto filter)
        {
            IEnumerable<CampaignMessageCountsDto> result = data;
            if (filter?.Sorting != null && !string.IsNullOrEmpty(filter.Sorting.Column) && !string.IsNullOrEmpty(filter.Sorting.Order))
            {
                string columnName = filter.Sorting.Column.ToLower();
                switch (columnName)
                {
                    case "sentcount":
                        result = filter.Sorting.Order.ToLower() == "desc" ? data.OrderByDescending(c => c.Sent) : data.OrderBy(c => c.Sent);
                        break;
                    case "deliveredcount":
                        result = filter.Sorting.Order.ToLower() == "desc" ? data.OrderByDescending(c => c.Delivered) : data.OrderBy(c => c.Delivered);
                        break;
                    case "readcount":
                        result = filter.Sorting.Order.ToLower() == "desc" ? data.OrderByDescending(c => c.Read) : data.OrderBy(c => c.Read);
                        break;
                    default:
                        break;
                }
            }
            return result.ToList();
        }
        #endregion

        public async Task<FileStreamResult> ExportCampaignByIdAsync(CampaignContactFilterDto campaignContactFilterDto)
        {
            var businessId = _userIdentityService.BusinessId;
            try
            {
                var filter = new CampaignContactFilterDto
                {
                    CampaignId = campaignContactFilterDto.CampaignId,
                    Status = campaignContactFilterDto.Status,
                    IncludeReplies = campaignContactFilterDto.IncludeReplies,
                    IsUndelivered = campaignContactFilterDto.IsUndelivered,  // Pass through the Undelivered flag
                    BusinessId = _userIdentityService.BusinessId
                };
                var campaignContactDetailsDto = await _campaignRespository.GetCampaignReportByStatusAsync(filter);

                var memoryStream = CreateCampaignReportInExcel(campaignContactDetailsDto);

                var fileResult = new FileStreamResult(memoryStream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
                {
                    FileDownloadName = $"CampaignReport-{DateTime.Now:yyyy_MM_dd-HH_mm_ss}.xlsx"
                };
                return fileResult;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Error getting campaign contacts by status using stored procedure: {ex.Message}", ex);
            }

        }

        public MemoryStream CreateCampaignReportInExcel(List<CampaignReportsDto> campaignReports)
        {
            MemoryStream stream = new MemoryStream();
            try
            {
                using (SpreadsheetDocument spreadsheetDocument = SpreadsheetDocument.Create(stream, SpreadsheetDocumentType.Workbook))
                {
                    WorkbookPart workbookPart = spreadsheetDocument.AddWorkbookPart();
                    workbookPart.Workbook = new Workbook();
                    WorksheetPart worksheetPart = workbookPart.AddNewPart<WorksheetPart>();
                    worksheetPart.Worksheet = new Worksheet(new SheetData());

                    Sheets sheets = spreadsheetDocument.WorkbookPart.Workbook.AppendChild(new Sheets());
                    Sheet sheet = new Sheet()
                    {
                        Id = spreadsheetDocument.WorkbookPart.GetIdOfPart(worksheetPart),
                        SheetId = 1,
                        Name = "Campaign Report"
                    };
                    sheets.Append(sheet);

                    Worksheet worksheet = worksheetPart.Worksheet;
                    SheetData sheetData = worksheet.GetFirstChild<SheetData>();

                    List<string> headers = new List<string>
              {
                  "Campaign Title", "Template Name", "Message Type", "Status",
                  "Scheduled Date", "State", "Contact Name", "Phone Number",
                  "Created By", "Error Message"
              };

                    Row headerRow = new Row();
                    foreach (var header in headers)
                    {
                        Cell cell = new Cell()
                        {
                            CellValue = new CellValue(header),
                            DataType = CellValues.String
                        };
                        headerRow.Append(cell);
                    }
                    sheetData.Append(headerRow);

                    foreach (var campaign in campaignReports)
                    {
                        Row row = new Row();
                        row.Append(CreateCell(campaign.Name ?? "N/A"));
                        row.Append(CreateCell(campaign.TemplateName ?? "N/A"));
                        row.Append(CreateCell(campaign.MessageType.ToString() ?? "N/A"));
                        row.Append(CreateCell(campaign.Status.ToString() ?? "N/A"));
                        row.Append(CreateCell(campaign.ScheduleDate?.ToString("yyyy-MM-dd") ?? "N/A"));
                        row.Append(CreateCell(campaign.State.ToString() ?? "N/A"));
                        row.Append(CreateCell(campaign.CustomerName ?? "N/A"));
                        row.Append(CreateCell(campaign.PhoneNumber ?? "N/A"));
                        row.Append(CreateCell(campaign.CreatedBy.ToString() ?? "N/A"));
                        row.Append(CreateCell(campaign.Error ?? "N/A"));
                        sheetData.Append(row);
                    }
                    worksheetPart.Worksheet.Save();
                    workbookPart.Workbook.Save();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message);
            }

            stream.Position = 0;
            return stream;
        }
        private static Cell CreateCell(string value)
        {
            return new Cell
            {
                CellValue = new CellValue(value),
                DataType = CellValues.String
            };
        }

        public async Task<CampaignUploadResultDto?> CampaignExcelUploadedFile(UploadFileDto fileDto)
        {
            try
            {
                var companyId = _userIdentityService.BusinessId;

                var wallet = await _walletService.GetWalletAsync(companyId);

                if (wallet?.Balance < 10)
                {
                    return new CampaignUploadResultDto
                    {
                        UploadResult = null,
                        ContactCount = 0,
                        HasInsufficientBalance = true,
                        HasRateLimitExceeded = false,
                        CanProceed = false,
                        RateLimitMessage = "Insufficient wallet balance. Minimum balance of 10 is required."
                    };
                }

                var totalContacts = await CountContactsInExcelFile(fileDto.File);

                // Get detailed rate limit information
                var rateLimitInfo = await GetRateLimitCountsAsync(companyId);

                // Extract unique phone numbers from Excel and check rate limits
                var uniquePhoneNumbers = await ExtractUniquePhoneNumbersFromExcel(fileDto.File);
                var uniqueCount = await CountNewContactsNotInRollingWindow(uniquePhoneNumbers, companyId);
                var alreadyMessagedCount = uniquePhoneNumbers.Count - uniqueCount;

                // Check wallet balance validation
                int walletAllowedContacts;
                bool canUploadByWallet;
                if (wallet.Balance >= 100)
                {
                    walletAllowedContacts = (int)(wallet.Balance + 50);
                    canUploadByWallet = totalContacts <= walletAllowedContacts;
                }
                else
                {
                    walletAllowedContacts = (int)(wallet.Balance + 5);
                    canUploadByWallet = totalContacts <= walletAllowedContacts;
                }

                // Check WhatsApp rate limit validation
                bool canUploadByRateLimit = uniqueCount <= rateLimitInfo.AvailableCount;

                // Both validations must pass
                bool canUpload = canUploadByWallet && canUploadByRateLimit;

                var result = new CampaignUploadResultDto
                {
                    ContactCount = totalContacts,
                    HasRateLimitExceeded = !canUploadByRateLimit,
                    HasInsufficientBalance = !canUploadByWallet,
                    UniqueCount = uniqueCount,
                    AvailableCount = rateLimitInfo.AvailableCount,
                    AlreadyConsumedCount = alreadyMessagedCount,
                    CanProceed = canUploadByWallet,
                    RateLimitInfo = rateLimitInfo,
                   // RateLimitMessage = rateLimitMessage,

                };

                if (canUploadByWallet)
                {
                    var uploadResult = await _blobStorageService.UploadAsync(fileDto);
                    result.UploadResult = uploadResult;
                }
                else
                {
                    result.UploadResult = null;
                }

                return result;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Error processing campaign Excel upload: {ex.Message}", ex);
            }
        }

        public async Task<object> FetchCampaignsByFilterAsync(CampaignFilter operations, string BusinessId, Guid UserId, int page, int per_page)
        {
            var business = await _appDbContext.BusinessDetailsMetas
                .AsNoTracking()
                .FirstOrDefaultAsync(t => t.BusinessId == BusinessId);

            var user = await _appDbContext.Users
                .AsNoTracking()
                .FirstOrDefaultAsync(u => u.Id == UserId);

            if (business == null || user == null)
                throw new Exception("Business or User with the specified ID does not exist.");

            var userRole = await (from ur in _appDbContext.UserRoles
                                  join r in _appDbContext.Roles on ur.RoleId equals r.Id
                                  where ur.Id == UserId
                                  select r.Name).FirstOrDefaultAsync();

            IQueryable<Campaign> query = _appDbContext.Campaigns.Where(c => c.BusinessId == business.BusinessId);

            if (userRole != "Admin" && userRole != "Owner")
                query = query.Where(c => c.UserId == UserId);

            if (!string.IsNullOrEmpty(operations?.Searching?.Value))
            {
                string value = operations.Searching.Value.ToLower();

                query = query.AsEnumerable()
                    .Where(m => (!string.IsNullOrEmpty(m.CampaignTitle) && m.CampaignTitle.ToLower().Contains(value)) ||
                                m.State.ToString().ToLower().Contains(value))
                    .AsQueryable();
            }

            if (operations?.Filtering?.Conditions != null)
            {
                foreach (var condition in operations.Filtering.Conditions)
                {
                    if (condition.Column == "State")
                    {
                        var val = condition.Value.ToLower();

                        query = query.AsEnumerable()
                                     .Where(i => i.State.ToString().ToLower() == val)
                                     .AsQueryable();
                    }
                    else if (condition.Column == "CreatedBy")
                    {
                        query = query.Where(i => i.Createdby.ToLower() == condition.Value.ToLower());
                    }
                }
            }

            if (operations.DateRangeFilters != null && operations.DateRangeFilters.Any())
            {
                foreach (var dateFilter in operations.DateRangeFilters)
                {
                    if (DateTime.TryParse(dateFilter.FromDate, out DateTime fromDate) &&
                        DateTime.TryParse(dateFilter.ToDate, out DateTime toDate))
                    {
                        toDate = toDate.Date.AddDays(1);

                        if (dateFilter.Column == "CreatedDate")
                        {
                            query = query.Where(c => c.CreatedDate >= fromDate && c.CreatedDate < toDate);
                        }

                        if (dateFilter.Column == "DateSetLive")
                        {
                            query = query.Where(c => c.CreatedDate >= fromDate && c.CreatedDate < toDate);
                        }
                    }
                }
            }

            if (!string.IsNullOrEmpty(operations?.Sorting?.Column))
            {
                var column = operations.Sorting.Column;
                var isDesc = operations.Sorting.Order?.ToLower() == "desc";
                var prop = typeof(Campaign).GetProperty(column, BindingFlags.IgnoreCase | BindingFlags.Public | BindingFlags.Instance);

                if (prop != null)
                {
                    var param = Expression.Parameter(typeof(Campaign), "x");
                    var property = Expression.Property(param, prop);
                    var converted = Expression.Convert(property, typeof(object));
                    var lambda = Expression.Lambda<Func<Campaign, object>>(converted, param);

                    query = isDesc ? query.OrderByDescending(lambda) : query.OrderBy(lambda);
                }
            }
            else
            {
                query = query.OrderByDescending(i => i.CreatedDate);
            }

            var totalCount = query.Count();

            var pagedData = query
                .Skip((page - 1) * per_page)
                .Take(per_page)
                .ToList();

            return new
            {
                Total = totalCount,
                Page = page,
                PerPage = per_page,
                Data = pagedData
            };
        }


        private async Task<int> CountContactsInExcelFile(IFormFile file)
        {
            try
            {
                ExcelPackage.LicenseContext = LicenseContext.NonCommercial;

                using (var stream = file.OpenReadStream())
                using (var package = new ExcelPackage(stream))
                {
                    var worksheet = package.Workbook.Worksheets[0];

                    if (worksheet.Dimension == null)
                    {
                        return 0;
                    }

                    var totalRows = worksheet.Dimension.End.Row - 1;

                    return Math.Max(0, totalRows);
                }
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Error counting contacts in Excel file: {ex.Message}", ex);
            }
        }
        public async Task<List<CampaignContactDetailDto>> GetCampaignContactsByStatusStoredProcAsync(CampaignContactFilterDto campaignContactFilterDto)
        {
            try
            {
                var filter = new CampaignContactFilterDto
                {
                    CampaignId = campaignContactFilterDto.CampaignId,
                    Status = campaignContactFilterDto.Status,
                    IncludeReplies = campaignContactFilterDto.IncludeReplies,
                    IsUndelivered = campaignContactFilterDto.IsUndelivered,  // Pass through the Undelivered flag
                    PageNumber = campaignContactFilterDto.PageNumber,
                    PageSize = campaignContactFilterDto.PageSize,
                    BusinessId = _userIdentityService.BusinessId
                };
                var campaignContactDetailsDto = await _campaignRespository.GetContactsFromCampaign(filter);

                return campaignContactDetailsDto;

            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Error getting campaign contacts by status using stored procedure: {ex.Message}", ex);
            }
        }

        public async Task<RateLimitCountDto> GetRateLimitCountsAsync(string businessId)
        {
            try
            {
                var tierStr = await _appDbContext.BusinessDetailsMetas.Where(b => b.BusinessId == businessId).Select(b => b.Tier).FirstOrDefaultAsync();

                long limitRate = !string.IsNullOrWhiteSpace(tierStr) &&
                long.TryParse(tierStr.Replace("TIER_", "", StringComparison.OrdinalIgnoreCase)
                                       .TrimEnd('K', 'k'), out var num)
                 ? (tierStr.EndsWith("K", StringComparison.OrdinalIgnoreCase) ? num * 1000 : num)
                 : 0;

                var adhokLimit = await _appDbContext.BusinessDetailsMetas.Where(b => b.BusinessId == businessId).Select(b => b.AdhocLimit).FirstOrDefaultAsync();
                var finalLimit = limitRate - adhokLimit;

                var limits = await _campaignRespository.GetRateLimitCountsAsync(businessId, finalLimit ?? 0);

                var RateLimitCountDto = new RateLimitCountDto
                {
                    MetaTotalLimit = limitRate,
                    EffectiveLimit = limits.AvailableCount + limits.ConsumeCount,
                    AdhocLimit = adhokLimit ?? 0,
                    AvailableCount = limits.AvailableCount,
                    ConsumeCount = limits.ConsumeCount,
                };
                return RateLimitCountDto;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Error getting rate limit counts: {ex.Message}", ex);
            }
        }

        public async Task<CheckRateLimitResponseDto> CheckCampaignRateLimitAndBalanceAsync(CheckRateLimitRequestDto request, string businessId)
        {
            try
            {
                var rateLimitInfo = await GetRateLimitCountsAsync(businessId);
                var phoneNumbers = await GetPhoneNumbersFromContactIds(request.ContactIds, businessId);
                var wallet = await _walletService.GetWalletAsync(businessId);
                                 
                var PhoneNumberChatStatusNotOpen = await GetPhoneNumberChatStatusNotOpen(request.ContactIds, businessId);
                var TotalPhoneNumberChatStatusNotOpen = PhoneNumberChatStatusNotOpen.Count;

                int totalContacts = phoneNumbers.Count;

                int walletAllowedContacts;
                bool CanProceedByBalance;

                if (wallet?.Balance < 10)
                {
                    walletAllowedContacts = (int)(wallet.Balance);
                    CanProceedByBalance = TotalPhoneNumberChatStatusNotOpen <= walletAllowedContacts;
                }
               else if (wallet.Balance >= 100)
                {
                    walletAllowedContacts = (int)(wallet.Balance + 50);
                    CanProceedByBalance = TotalPhoneNumberChatStatusNotOpen <= walletAllowedContacts;
                }
                else
                {
                    walletAllowedContacts = (int)(wallet.Balance + 5);
                    CanProceedByBalance = TotalPhoneNumberChatStatusNotOpen <= walletAllowedContacts;
                }
                // Count contacts that have NOT been messaged in the last 24 hours
                var uniqueCount = await CountNewContactsNotInRollingWindow(phoneNumbers, businessId);
                var alreadyMessagedCount = phoneNumbers.Count - uniqueCount;
                // Determine if campaign can proceed
                var canProceedRateLimit = uniqueCount <= rateLimitInfo.AvailableCount;
                // Generate appropriate message
     
                return new CheckRateLimitResponseDto
                {
                    UniqueCount = uniqueCount,
                    TotalContactsProvided = request.ContactIds.Count,
                    AlreadyMessagedCount = alreadyMessagedCount,
                    CanProceedByRateLimit = canProceedRateLimit,
                    CanProceedByBalance = CanProceedByBalance,
                    RateLimitInfo = rateLimitInfo
                };
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Error checking campaign rate limit: {ex.Message}", ex);
            }
        }

        public async Task<bool> ValidateExcelFileAgainstRateLimitAsync(UploadFileDto fileDto, string businessId)
        {
            try
            {
                if (fileDto?.File == null)
                {
                    throw new ArgumentNullException(nameof(fileDto), "File cannot be null");
                }

                // Get current rate limit information
                var rateLimitCounts = await GetRateLimitCountsAsync(businessId);

                if (rateLimitCounts == null)
                {
                    throw new InvalidOperationException("Unable to retrieve rate limit information");
                }

                // Extract unique phone numbers from Excel file
                var uniquePhoneNumbers = await ExtractUniquePhoneNumbersFromExcel(fileDto.File);

                if (uniquePhoneNumbers.Count == 0)
                {
                    return true; // No contacts to validate
                }

                // Check how many of these contacts have NOT been messaged in the last 24 hours
                var newContactsCount = await CountNewContactsNotInRollingWindow(uniquePhoneNumbers, businessId);

                // The new contacts count should not exceed the available rate limit
                return newContactsCount <= rateLimitCounts.AvailableCount;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Error validating Excel file against rate limit: {ex.Message}", ex);
            }
        }

        private async Task<List<string>> ExtractUniquePhoneNumbersFromExcel(IFormFile file)
        {
            try
            {
                ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
                var uniquePhoneNumbers = new List<string>();

                using (var stream = file.OpenReadStream())
                using (var package = new ExcelPackage(stream))
                {
                    var worksheet = package.Workbook.Worksheets[0];

                    if (worksheet.Dimension == null)
                    {
                        return uniquePhoneNumbers;
                    }

                    var totalRows = worksheet.Dimension.End.Row;
                    var totalColumns = worksheet.Dimension.End.Column;

                    // Find phone number columns by checking header row
                    var phoneNumberColumns = new List<int>();
                    for (int col = 1; col <= totalColumns; col++)
                    {
                        var headerValue = worksheet.Cells[1, col].Value?.ToString()?.ToLower().Trim();
                        if (!string.IsNullOrEmpty(headerValue) &&
                            (headerValue.Contains("phone") || headerValue.Contains("contact") ||
                             headerValue.Contains("mobile") || headerValue.Contains("number")))
                        {
                            phoneNumberColumns.Add(col);
                        }
                    }

                    // If no phone columns found by header, assume first column contains phone numbers
                    if (!phoneNumberColumns.Any())
                    {
                        phoneNumberColumns.Add(1);
                    }

                    // Extract phone numbers from identified columns
                    for (int row = 2; row <= totalRows; row++) // Start from row 2 to skip header
                    {
                        foreach (var col in phoneNumberColumns)
                        {
                            var cellValue = worksheet.Cells[row, col].Value?.ToString()?.Trim();

                            if (!string.IsNullOrWhiteSpace(cellValue))
                            {
                                // Clean and normalize phone number
                                var cleanedPhoneNumber = CleanPhoneNumber(cellValue);

                                if (!string.IsNullOrWhiteSpace(cleanedPhoneNumber))
                                {
                                    uniquePhoneNumbers.Add(cleanedPhoneNumber);
                                }
                            }
                        }
                    }
                }

                return uniquePhoneNumbers;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Error extracting phone numbers from Excel file: {ex.Message}", ex);
            }
        }

        private async Task<int> CountNewContactsNotInRollingWindow(List<string> phoneNumbers, string businessId)
        {
            try
            {
                if (!phoneNumbers.Any())
                    return 0;

                // Calculate 24 hours ago timestamp
                var twentyFourHoursAgo = DateTime.UtcNow.AddHours(-24);

                // Normalize phone numbers (remove + and country codes for comparison)
                var normalizedPhoneNumbers = phoneNumbers
                    .Select(p => p.Replace("+", "").Trim())
                    .Where(p => !string.IsNullOrWhiteSpace(p))
                    .ToList();

                if (!normalizedPhoneNumbers.Any())
                    return 0;

                // Query to find contacts that HAVE been messaged in the last 24 hours
                var contactsMessagedInLast24Hours = await _appDbContext.Conversations
                    .Where(c => c.From == businessId && // Messages sent by business
                                (c.Status == EngagetoEntities.Enums.ConvStatus.sent || c.Status == ConvStatus.delivered || c.Status == ConvStatus.read) && // Successfully sent
                                c.CreatedAt >= twentyFourHoursAgo && // Within last 24 hours
                                normalizedPhoneNumbers.Contains(c.To.Replace("+", ""))) // Contact is in our Excel list
                    .Select(c => c.To.Replace("+", ""))
                    .Distinct()
                    .ToListAsync();

                // Count contacts that have NOT been messaged (new contacts for rate limiting)
                var newContactsCount = normalizedPhoneNumbers
                    .Except(contactsMessagedInLast24Hours, StringComparer.OrdinalIgnoreCase)
                    .Count();

                return newContactsCount;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Error counting new contacts not in rolling window: {ex.Message}", ex);
            }
        }

        private async Task<List<string>> GetPhoneNumbersFromContactIds(List<string> contactIds, string businessId)
        {
            try
            {
                var phoneNumbers = new List<string>();

                var filters = new List<RequestFilterDto>
                {
                    new RequestFilterDto("BusinessId", businessId, "="),
                    new RequestFilterDto("IsActive", "true", "="),
                    new RequestFilterDto("ContactId", contactIds, "in"),
                };

                // Get contacts using generic repository
                var contacts = await _genericRepository.GetRecordByRequestFilter<Contacts>(
                    filters,
                    "Contacts",
                    columns: new List<string> { "ContactId", "CountryCode", "Contact" }
                );

                // Filter by contact IDs and extract phone numbers
                foreach (var contact in contacts)
                {
                    var fullPhoneNumber = $"{contact.CountryCode}{contact.Contact}";
                    if (!string.IsNullOrWhiteSpace(fullPhoneNumber))
                    {
                        phoneNumbers.Add(fullPhoneNumber);
                    }
                }

                return phoneNumbers;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Error getting phone numbers from contact IDs: {ex.Message}", ex);
            }
        }

        private string GenerateRateLimitMessage(int uniqueCount, int availableCount, bool canProceed)
        {
            if (canProceed)
            {
                if (uniqueCount == 0)
                {
                    return "All contacts have been messaged in the last 24 hours. Campaign can proceed without consuming rate limit.";
                }
                return $"Campaign can proceed. {uniqueCount} new contacts will be messaged, {availableCount - uniqueCount} slots will remain available.";
            }
            else
            {
                var excess = uniqueCount - availableCount;
                return $"Rate limit exceeded. {uniqueCount} contacts need messaging but only {availableCount} slots available. Consider splitting campaign or scheduling {excess} contacts for later.";
            }
        }

        private string? CleanPhoneNumber(string phoneNumber)
        {
            if (string.IsNullOrWhiteSpace(phoneNumber))
                return null;

            // Remove common non-numeric characters
            var cleaned = phoneNumber.Replace(" ", "")
                                   .Replace("-", "")
                                   .Replace("(", "")
                                   .Replace(")", "")
                                   .Replace(".", "")
                                   .Trim();

            // Ensure it starts with + if it doesn't already
            if (!cleaned.StartsWith("+") && cleaned.Length > 0)
            {
                // If it starts with a digit, add default country code
                if (char.IsDigit(cleaned[0]))
                {
                    cleaned = "+91" + cleaned; // Default to India country code, adjust as needed
                }
            }

            // Basic validation: should be at least 10 digits after cleaning
            var digitsOnly = new string(cleaned.Where(char.IsDigit).ToArray());
            if (digitsOnly.Length < 10)
                return null;

            return cleaned;
        }
        private async Task<List<string>> GetPhoneNumberChatStatusNotOpen(List<string> contactIds, string businessId)
        {
            try
            {
                var phoneNumbers = new List<string>();

                var filters = new List<RequestFilterDto>
                {
                    new RequestFilterDto("BusinessId", businessId, "="),
                    new RequestFilterDto("IsActive", "true", "="),
                    new RequestFilterDto("ContactId", contactIds, "in"),
                    new RequestFilterDto("ChatStatus", 0, "!=")
                };

                // Get contacts using generic repository
                var contacts = await _genericRepository.GetRecordByRequestFilter<Contacts>(
                    filters,
                    "Contacts",
                    columns: new List<string> { "ContactId", "CountryCode", "Contact" }
                );

                // Filter by contact IDs and extract phone numbers
                foreach (var contact in contacts)
                {
                    var fullPhoneNumber = $"{contact.CountryCode}{contact.Contact}";
                    if (!string.IsNullOrWhiteSpace(fullPhoneNumber))
                    {
                        phoneNumbers.Add(fullPhoneNumber);
                    }
                }

                return phoneNumbers;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Error getting phone numbers from contact IDs: {ex.Message}", ex);
            }
        }

    }
}