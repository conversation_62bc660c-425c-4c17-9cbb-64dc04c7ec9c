using System.ComponentModel.DataAnnotations;

namespace EngagetoEntities.Dtos.CampaignDtos
{
    public class CheckRateLimitRequestDto
    {
        [Required]
        public List<string> ContactIds { get; set; } = new();
    }
    public class CheckRateLimitResponseDto
    {
        public int UniqueCount { get; set; }
        public int TotalContactsProvided { get; set; }
        public int AlreadyMessagedCount { get; set; }
        public bool CanProceedByRateLimit { get; set; }
        public bool CanProceedByBalance { get; set; }   
        public RateLimitCountDto RateLimitInfo { get; set; } = new();
    }
}
