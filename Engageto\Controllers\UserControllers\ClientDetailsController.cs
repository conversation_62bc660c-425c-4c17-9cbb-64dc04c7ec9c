﻿using EngagetoContracts.UserContracts;
using EngagetoEntities.Dtos.ApiResponseDtos;
using EngagetoEntities.Dtos.BusinessDetailsDtos;
using EngagetoEntities.Entities;
using EngagetoEntities.UserEntities.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Security.Claims;

namespace UserManagementService.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class ClientDetailsController : ControllerBase
    {
        private readonly IClientDetailsService _clientDetailsService;
        private readonly ApplicationDBContext _context;

        public ClientDetailsController(IClientDetailsService clientDetailsService, ApplicationDBContext context)
        {
            _clientDetailsService = clientDetailsService;
            _context = context;
        }

        [HttpPost("add-client")]
        [Authorize]
        //[AuthorizeRoles(RoleConstants.Admin, RoleConstants.Owner, RoleConstants.Teammate)]
        // [AuthorizeMenu("addClient")]
        public async Task<IActionResult> AddClient([FromForm] Ahex_CRMClientDetailsDto clientDetailsDto)
        {
            try
            {
                var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);

                if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
                {
                    return BadRequest(new { Message = "Invalid current user." });
                }
                var existingCompany = await _context.Ahex_CRM_BusinessDetails
                                          .FirstOrDefaultAsync(c => c.CompanyLegalName == clientDetailsDto.CompanyName);

                if (existingCompany != null)
                {
                    return BadRequest(new { Message = "A company with this name already exists." });
                }

                var existingMail = await _context.Ahex_CRM_Users
                                         .FirstOrDefaultAsync(c => c.EmailAddress == clientDetailsDto.Email);
                if (existingMail != null)
                {
                    return BadRequest(new { Message = "User(owner) with this email is already registered." });
                }
                if (clientDetailsDto.BusinessEmail != "" && clientDetailsDto.BusinessEmail != null)
                {
                    var businessDetails = await _context.Ahex_CRM_BusinessDetails
                                                         .FirstOrDefaultAsync(c => c.BusinessEmail == clientDetailsDto.BusinessEmail);

                    if (businessDetails != null)
                    {
                        return BadRequest(new { Message = "Company with this email is already registered." });
                    }
                }
                /* var existingMobile = await _context.Ahex_CRM_Users.FirstOrDefaultAsync(u => u.PhoneNumber == clientDetailsDto.PhoneNumber);
                 if (existingMobile != null)
                 {
                     throw new InvalidOperationException("Phone number is already registered.");
                 }*/
                if (string.IsNullOrWhiteSpace(clientDetailsDto.ClientName) || clientDetailsDto.ClientName.Length < 1 || clientDetailsDto.ClientName.Length > 100)
                {
                    return BadRequest(new { Message = "Client name must be between 1 and 100 characters." });
                }
                if (string.IsNullOrWhiteSpace(clientDetailsDto.CompanyName) || clientDetailsDto.CompanyName.Length < 1 || clientDetailsDto.CompanyName.Length > 100)
                {
                    return BadRequest(new { Message = "Client name must be between 1 and 100 characters." });
                }
                bool isClientAdded = await _clientDetailsService.AddClientAsync(currentUserId, clientDetailsDto);

                if (isClientAdded)
                {
                    return Ok(new { Message = "Client added successfully." });
                }
                else
                {
                    return BadRequest(new { Message = "Failed to add client." });
                }
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { Message = ex.Message });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { Message = $"Internal Server Error: {ex.Message}" });
            }
        }



        [HttpPost("anonymous/add-client")]
        [AllowAnonymous]
        [EnableCors("CustomCors")]
        public async Task<IActionResult> AddClientAnonymous([FromForm] Ahex_CRMClientDetailsDto clientDetailsDto)
        {
            try
            {

                var existingCompany = await _context.Ahex_CRM_BusinessDetails
                                          .FirstOrDefaultAsync(c => c.CompanyLegalName == clientDetailsDto.CompanyName);

                if (existingCompany != null)
                {
                    return BadRequest(new { Message = "A company with this name already exists." });
                }

                var existingMail = await _context.Ahex_CRM_Users
                                         .FirstOrDefaultAsync(c => c.EmailAddress == clientDetailsDto.Email);
                if (existingMail != null)
                {
                    return BadRequest(new { Message = "User(owner) with this email is already registered." });
                }
                if (clientDetailsDto.BusinessEmail != "" && clientDetailsDto.BusinessEmail != null)
                {
                    var businessDetails = await _context.Ahex_CRM_BusinessDetails
                                                         .FirstOrDefaultAsync(c => c.BusinessEmail == clientDetailsDto.BusinessEmail);

                    if (businessDetails != null)
                    {
                        return BadRequest(new { Message = "Company with this email is already registered." });
                    }
                }
                if (string.IsNullOrWhiteSpace(clientDetailsDto.ClientName) || clientDetailsDto.ClientName.Length < 1 || clientDetailsDto.ClientName.Length > 100)
                {
                    return BadRequest(new { Message = "Client name must be between 1 and 100 characters." });
                }
                if (string.IsNullOrWhiteSpace(clientDetailsDto.CompanyName) || clientDetailsDto.CompanyName.Length < 1 || clientDetailsDto.CompanyName.Length > 100)
                {
                    return BadRequest(new { Message = "Client name must be between 1 and 100 characters." });
                }
                bool isClientAdded = await _clientDetailsService.AddClientAnonymousAsync(clientDetailsDto);

                if (isClientAdded)
                {
                    return Ok(new { Message = "Client added successfully." });
                }
                else
                {
                    return BadRequest(new { Message = "Failed to add client." });
                }
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { Message = ex.Message });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { Message = $"Internal Server Error: {ex.Message}" });
            }
        }

        [HttpGet("client-details/{businessId}")]
        [Authorize]
        public async Task<IActionResult> GetClientDetailsAsync(string businessId)
        {
            try
            {
                var result = await _clientDetailsService.GetClientDetailsAsync(businessId);
                if (result == null)
                {
                    return BadRequest(new ApiResponse<BusinessMetaDetailsDto>
                    {
                        Success = false,
                        Message = "Failed to fetch client details."
                    });
                }

                return Ok(new ApiResponse<BusinessMetaDetailsDto>
                {
                    Success = true,
                    Data = result
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new ApiResponse<object>
                {
                    Success = false,
                    Message = ex.Message
                });
            }
        }

        [HttpPut("update-client/{clientId}")]
        // [AuthorizeRoles(RoleConstants.Admin, RoleConstants.Owner, RoleConstants.Teammate)]
        [Authorize]
        // [Authorize("editClient")]
        public async Task<IActionResult> UpdateClient(Guid clientId, [FromForm] Ahex_CRMClientDetailsUpdateDto updateClientDetailsDto)
        {
            try
            {
                var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);

                if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
                {
                    return BadRequest(new { Message = "Invalid current user." });
                }
                var clientDetails = await _context.Ahex_CRM_BusinessDetails
                    .FirstOrDefaultAsync(x => x.Id == clientId)
                    ?? throw new Exception("ClientId is not valid");

                if (clientDetails.BusinessEmail?.ToLowerInvariant() != updateClientDetailsDto.BusinessEmail?.ToLowerInvariant() &&
                    _context.Ahex_CRM_BusinessDetails.Any(x => x.BusinessEmail == updateClientDetailsDto.BusinessEmail))
                {
                    return BadRequest(new { Message = "Client email already exist in our records." });
                }
                var result = await _clientDetailsService.UpdateClientAsync(currentUserId, clientId, updateClientDetailsDto);

                if (result)
                {
                    return Ok(new { Message = "Client updated successfully." });
                }
                else
                {
                    return BadRequest(new { Message = "Failed to update client." });

                }
            }

            catch (Exception ex)
            {
                return StatusCode(500, new { Message = $"Internal Server Error: {ex.Message}" });
            }
        }



        [HttpDelete("delete-client/{clientId}")]
        //[AuthorizeRoles(RoleConstants.Admin, RoleConstants.Owner, RoleConstants.Teammate)]
        [Authorize]
        public async Task<IActionResult> DeleteCompany([FromRoute] Guid clientId)
        {
            try
            {
                var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);

                if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
                {
                    return BadRequest(new { Message = "Invalid current user." });
                }

                var result = await _clientDetailsService.DeleteClientAsync(currentUserId, clientId);

                if (result)
                {
                    return Ok(new { Message = "Client deleted successfully." });
                }
                else
                {
                    return BadRequest(new { Message = "Failed to delete client." });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { Message = $"Internal Server Error: {ex.Message}" });
            }
        }
        [HttpGet("get-client/{clientId}")]
        // [AuthorizeRoles(RoleConstants.Admin, RoleConstants.Owner, RoleConstants.Teammate)]
        [Authorize]
        public async Task<IActionResult> GetClient(Guid clientId)
        {
            try
            {
                var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);

                if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
                {
                    return BadRequest(new { Message = "Invalid current user." });
                }

                var company = await _clientDetailsService.GetClientByIdAsync(currentUserId, clientId);

                if (company != null)
                {
                    return Ok(company);
                }
                else
                {
                    return NotFound(new { Message = "Client not found." });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { Message = "Internal Server Error" });
            }
        }
        [HttpGet("get-all-clients")]
        // [AuthorizeRoles(RoleConstants.Admin, RoleConstants.Owner, RoleConstants.Teammate)]
        [Authorize]
        public async Task<IActionResult> GetAllClients(
          [FromQuery] string searchQuery = null,
          [FromQuery] bool? includeInactive = true,
          [FromQuery] string sortBy = null,
          [FromQuery] bool isSortAscending = true)
        {
            try




            {
                var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);

                if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
                {
                    return BadRequest(new { Message = "Invalid current user." });
                }

                bool includeInactiveValue = includeInactive ?? true;

                var companies = await _clientDetailsService.GetAllClientsAsync(currentUserId, searchQuery, includeInactiveValue, sortBy, isSortAscending);

                if (companies != null)
                {
                    return Ok(companies);
                }
                else
                {
                    return BadRequest(new { Message = "Failed to retrieve companies." });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { Message = $"Internal Server Error: {ex.Message}" });
            }
        }

        [HttpGet("get-tenant-accounts")]
        [AllowAnonymous]
        public async Task<IActionResult> GetTenantAccountsAsync(string tenantId)
        {
            try
            {
                var result = await _clientDetailsService.GetTenantAccountsAsync(tenantId);
                return Ok(new ApiResponse<List<TenantDtos>>(true, "tenants details", result));
            }
            catch (Exception ex) 
            {
                return StatusCode(500,new ApiResponse<object>(false, "Internal Server Error:", ex.InnerException));
            }
        }

        [HttpPut("UpdateRateLimit")]
        public async Task<IActionResult> UpdateRateLimit([FromQuery] string businessId, [FromQuery] int  AdhocLimit )
        {
            try
            {
                var result = await _clientDetailsService.UpdateRateLimit(businessId, AdhocLimit);
                return Ok(new ApiResponse<bool>(true, "Rate limit updated successfully.", result));
            }
             catch(Exception ex)
            {
                return StatusCode(500, new ApiResponse<object>(false, "Internal Server Error:", ex.InnerException));
            }
        }
    }
}
